OUTDIR = build
TARGET = $(OUTDIR)/3786

# sudo apt-get install gcc-aarch64-linux-gnu
# sudo apt-get install make

# arm-linux-gnueabihf-gcc -v #gcc version 9.4.0 √

# arm64打包
CC = sudo aarch64-linux-gnu-gcc

# 声明调用库
LIBS = 


# 导入so库
LDFLAGS = 

# 优化代码模式         
CFLAGS = -O2

# 静态编译
PATTERN = 
# make
main: main.c
	mkdir -p $(OUTDIR)
	$(CC) $(CFLAGS) -o $(TARGET) main.c $(LDFLAGS) $(LIBS) $(PATTERN)

# make strip
strip:
	sudo aarch64-linux-gnu-strip $(TARGET)

# make clean
clean:
	rm -rf $(OUTDIR)

.PHONY: clean strip