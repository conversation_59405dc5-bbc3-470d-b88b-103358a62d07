ID="unisoc-initgc"
NAME="unisoc-initgc-distro"
VERSION="udx710-module+unisoc-initgc-1.0+W24.25.1:07.10.36+user+native (sumo)"
VERSION_ID="udx710-module-unisoc-initgc-1.0-w24.25.1:07.10.36-user-native"
PRETTY_NAME="unisoc-initgc-distro udx710-module+unisoc-initgc-1.0+W24.25.1:07.10.36+user+native (sumo)"
MM_PROJECT_NAME="udx710_l71mb-01-tm02_tmbg_tm70a_h3_256mb_5g_nr_user_20240617151013"
BUILD_MODE="user"
VENDOR="MM"
MM_SOFTWARE_VERSION="BY 小陳同学"
MM_HARDWARE_VERSION="L71MB"
MM_CUSTOM_PRODUCT_MODEL="电小果(暗控修复)"
MM_CUSTOM_DEVICE_TYPE="MIFI"
MM_PO_CONFIG="po_modify_autotest_light po_modify_connman_for_aic8800 po_modify_recovery_mode_led po_add_ata_lcm_test_p6 po_d80_dw_ata po_aic8800dw_switchto_dwh po_l70_wifi_board_config po_add_fota_logo_gx po_aic_dwh_d80_concurrent po_modify_apn po_modify_ui_for_zx po_lvgl_support_gif po_modify_lcm_para_tm po_3sim_autotest po_l71_bsp_lcm_d80_sim_det_switch_ml_bat po_modify_libmm_config_tm po_web_config_aic8800d80_tm po_mumu_target_config_aic8800d80_tm po_modify_app_config_aic8800d80_tm po_modify_app_cmd_json_3sim_tm po_modify_lcm_logo_gx_zx po_modify_fota_recovery_logo"
MM_MODEM_NAME="modem_l71-01"
MM_SIGN_NAME="tm"
CUSTOM_FOTA_SERVICE_OEM="mumu"
CUSTOM_FOTA_UNIQUE="sn"
CUSTOM_FOTA_SERVICE_VERSION="20240617151013"
PLATFORM_NAME="udx710_l71mb-01-tm02_tmbg_tm70a_h3_256mb_5g_nr"
FOTA_SERVER_URL="ota.mumuiot.com"
FOTA_SERVER_PORT="8181"
